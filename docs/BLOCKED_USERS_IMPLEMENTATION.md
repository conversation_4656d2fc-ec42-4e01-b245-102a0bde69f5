# Blocked Users Implementation

This document outlines the implementation of the blocked users functionality that integrates with the global state management system.

## Overview

The blocked users feature allows users to block/unblock other users and maintains this state globally throughout the application. The implementation includes:

1. **Global State Management** - Using Zustand store for blocked users
2. **Enhanced Hooks** - Improved `useGetBlockedUsers` and `useBlockUser` hooks
3. **Profile Integration** - Updated ProfileHeader component to show block status
4. **Automatic Initialization** - Blocked users are loaded when the user authenticates

## Files Modified/Created

### New Files

1. **`stores/useUserStore.ts`** - Zustand store for user-related global state
2. **`hooks/userHooks/useInitializeBlockedUsers.tsx`** - Hook to initialize blocked users on app start
3. **`hooks/userHooks/__tests__/useGetBlockedUsers.test.ts`** - Tests for the blocked users hook

### Modified Files

1. **`hooks/userHooks/useGetBlockedUsers.tsx`** - Enhanced with global state integration
2. **`hooks/userHooks/useBlockUser.tsx`** - Updated to work with global state
3. **`components/ProfileComponents/ProfileHeader.tsx`** - Added blocked user detection
4. **`services/userAPI.ts`** - Added TypeScript interfaces for blocked users API
5. **`context/AuthContext.tsx`** - Added blocked users initialization
6. **`app/(protected)/(tabs)/profile.tsx`** - Added blocked users hook usage

## Key Features

### 1. Global State Management

The `useUserStore` provides:
- `blockedUsers`: Array of blocked users
- `setBlockedUsers`: Set the entire blocked users list
- `addBlockedUser`: Add a user to blocked list
- `removeBlockedUser`: Remove a user from blocked list
- `isUserBlocked`: Check if a user is blocked
- `clearBlockedUsers`: Clear all blocked users

### 2. Enhanced useGetBlockedUsers Hook

Features:
- **Automatic Global State Updates**: Updates the global store when data is fetched
- **Proper Caching**: 5-minute stale time, 10-minute cache time
- **Error Handling**: Exponential backoff retry strategy
- **TypeScript Support**: Fully typed with proper interfaces
- **Conditional Fetching**: Only fetches when user is authenticated

### 3. Enhanced useBlockUser Hook

Features:
- **Optimistic Updates**: Immediately updates global state
- **Proper Cache Invalidation**: Invalidates relevant queries
- **Better API**: Separate `blockUser`, `unblockUser`, and `toggleBlockUser` methods
- **Loading States**: Separate loading states for block/unblock operations

### 4. Profile Integration

The ProfileHeader component now:
- **Detects Blocked Status**: Uses global state to check if user is blocked
- **Dynamic Menu Items**: Shows "Block User" or "Unblock User" based on status
- **Optimistic UI**: Immediately reflects changes in the UI

### 5. Automatic Initialization

- **App-Level Loading**: Blocked users are loaded when the user authenticates
- **Context Integration**: Integrated into AuthContext for automatic initialization
- **Profile-Level Loading**: Also loads when viewing own profile

## Usage Examples

### Using the Global Store

```typescript
import { useUserStore } from '@/stores/useUserStore';

const MyComponent = () => {
  const { isUserBlocked, blockedUsers } = useUserStore();
  
  const userId = 'some-user-id';
  const blocked = isUserBlocked(userId);
  
  return (
    <Text>{blocked ? 'User is blocked' : 'User is not blocked'}</Text>
  );
};
```

### Using the Enhanced Hooks

```typescript
import useGetBlockedUsers from '@/hooks/userHooks/useGetBlockedUsers';
import useBlockUser from '@/hooks/userHooks/useBlockUser';

const BlockedUsersScreen = () => {
  const { blockedUsers, isLoading } = useGetBlockedUsers();
  const { blockUser, unblockUser, isLoading: isBlocking } = useBlockUser();
  
  const handleBlock = (userId: string) => {
    blockUser(userId, {
      username: 'user123',
      displayName: 'User Name',
      image: 'avatar.jpg'
    });
  };
  
  return (
    <View>
      {blockedUsers.map(user => (
        <UserItem 
          key={user._id} 
          user={user} 
          onUnblock={() => unblockUser(user._id)}
        />
      ))}
    </View>
  );
};
```

## API Integration

The implementation expects the following API responses:

### Get Blocked Users API
```typescript
interface IBlockedUsersAPIResponse {
  success: boolean;
  message: string;
  blockedUsers: {
    _id: string;
    username: string;
    displayName: string;
    image?: string;
    blockedAt?: Date;
  }[];
}
```

### Block/Unblock User APIs
Standard success/error responses with proper error handling.

## Testing

The implementation includes unit tests for the core functionality:
- Hook initialization
- State management
- Conditional enabling/disabling
- Error handling

Run tests with:
```bash
npm test hooks/userHooks/__tests__/useGetBlockedUsers.test.ts
```

## Performance Considerations

1. **Efficient Caching**: Uses React Query's caching with appropriate stale times
2. **Conditional Fetching**: Only fetches when necessary
3. **Optimistic Updates**: Immediate UI feedback without waiting for API
4. **Memoized Computations**: Uses useMemo for expensive operations

## Future Enhancements

1. **Offline Support**: Cache blocked users for offline access
2. **Real-time Updates**: WebSocket integration for real-time block status updates
3. **Bulk Operations**: Support for blocking/unblocking multiple users
4. **Block Reasons**: Add support for block reasons and categories
