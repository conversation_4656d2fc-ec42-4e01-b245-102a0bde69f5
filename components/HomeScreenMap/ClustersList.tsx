import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { isPointCluster } from 'react-native-clusterer';
import { Marker } from 'react-native-maps';
import { COLORS } from '@/constants/Theme';
import CustomText from '../ui/CustomText';
import { Ionicons } from '@expo/vector-icons';

const ClustersList = () => {
  
    
      return points
        .map((point, index) => {
          try {
            const isCluster = isPointCluster(point);
            const coords = point.geometry.coordinates;
            const lat = coords[1];
            const lng = coords[0];

            // Validate coordinates
            if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
              console.warn(
                `📍 Invalid coordinates for point ${index}:`,
                coords,
              );
              return null;
            }

            if (isCluster) {
              // Enhanced cluster marker
              const pointCount = point.properties.point_count || 0;
              console.log(`📍 Rendering cluster with ${pointCount} points`);

              // Determine cluster size for dynamic styling
              const getClusterSize = (count: number) => {
                if (count >= 100) return 'large';
                if (count >= 10) return 'medium';
                return 'small';
              };

              const clusterSize = getClusterSize(pointCount);

              // Get styles based on cluster size
              const getClusterStyles = (size: string) => {
                switch (size) {
                  case 'large':
                    return {
                      container: styles.clusterLarge,
                      background: styles.clusterBackgroundLarge,
                      badge: styles.clusterBadgeLarge,
                      text: styles.clusterTextLarge,
                      pulse: styles.clusterPulseLarge,
                    };
                  case 'medium':
                    return {
                      container: styles.clusterMedium,
                      background: styles.clusterBackgroundMedium,
                      badge: styles.clusterBadgeMedium,
                      text: styles.clusterTextMedium,
                      pulse: styles.clusterPulseMedium,
                    };
                  default:
                    return {
                      container: styles.clusterSmall,
                      background: styles.clusterBackgroundSmall,
                      badge: styles.clusterBadgeSmall,
                      text: styles.clusterTextSmall,
                      pulse: styles.clusterPulseSmall,
                    };
                }
              };

              const clusterStyles = getClusterStyles(clusterSize);

              return (
                <Marker
                  key={`cluster-${point.id || index}`}
                  onPress={() => {
                    try {
                      const region = point.properties.getExpansionRegion();
                      mapRef.current?.animateToRegion(region, 500);
                    } catch (error) {
                      console.error('📍 Error expanding cluster:', error);
                    }
                  }}
                  coordinate={{ latitude: lat, longitude: lng }}
                  tracksViewChanges={false}
                  anchor={{ x: 0.5, y: 0.5 }}
                  zIndex={1000}
                >
                  <View
                    style={[styles.clusterContainer, clusterStyles.container]}
                  >
                    {/* Pulse animation ring (behind everything) */}
                    <View style={clusterStyles.pulse} />

                    {/* Cluster background with gradient effect */}
                    <View style={clusterStyles.background} />

                    {/* Cluster icon/image */}
                    <View style={styles.clusterIconContainer}>
                      <Ionicons
                        name='location'
                        size={
                          32
                        }
                        color={COLORS.white[50]}
                      />
                    </View>

                    {/* Count badge */}
                    <View style={clusterStyles.badge}>
                      <CustomText style={clusterStyles.text}>
                        {pointCount}
                      </CustomText>
                    </View>
                  </View>
                </Marker>
              );
            } else {
              // Individual activity marker
              console.log(
                `📍 Rendering individual marker: ${point.properties.name}`,
              );
              return (
                <MemoizedMarker
                  key={`point-${point.properties._id || index}`}
                  id={point.properties._id || `activity-${index}`}
                  image={point.properties.images?.[0] || ''}
                  name={point.properties.name || 'Activity'}
                  latitude={lat}
                  longitude={lng}
                />
              );
            }
          } catch (error) {
            console.error(`📍 Error rendering point ${index}:`, error);
            return null;
          }
        })
        .filter(Boolean); // Remove null entries
    }, [points, mapRef])

};

export default ClustersList;

const styles = StyleSheet.create({
    // Enhanced cluster marker styles with image tag design
  clusterContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },

  // Small cluster (2-9 items)
  clusterSmall: {
    width: 45,
    height: 45,
  },
  clusterBackgroundSmall: {
    position: 'absolute',
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: '#FF6B6B', // Red for small clusters
    borderWidth: 3,
    borderColor: COLORS.white[50],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 5,
    elevation: 8,
  },
  clusterBadgeSmall: {
    position: 'absolute',
    top: -5,
    right: -5,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.white[50],
  },
  clusterTextSmall: {
    color: COLORS.white[50],
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  clusterPulseSmall: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 107, 107, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(255, 107, 107, 0.4)',
  },

  // Medium cluster (10-99 items)
  clusterMedium: {
    width: 55,
    height: 55,
  },
  clusterBackgroundMedium: {
    position: 'absolute',
    width: 55,
    height: 55,
    borderRadius: 27.5,
    backgroundColor: '#4ECDC4', // Teal for medium clusters
    borderWidth: 3,
    borderColor: COLORS.white[50],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.5,
    shadowRadius: 6,
    elevation: 10,
  },
  clusterBadgeMedium: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#26A69A',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.white[50],
  },
  clusterTextMedium: {
    color: COLORS.white[50],
    fontSize: 11,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  clusterPulseMedium: {
    position: 'absolute',
    width: 62,
    height: 62,
    borderRadius: 31,
    backgroundColor: 'rgba(78, 205, 196, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(78, 205, 196, 0.4)',
  },

  // Large cluster (100+ items)
  clusterLarge: {
    width: 65,
    height: 65,
  },
  clusterBackgroundLarge: {
    position: 'absolute',
    width: 65,
    height: 65,
    borderRadius: 32.5,
    backgroundColor: '#45B7D1', // Blue for large clusters
    borderWidth: 4,
    borderColor: COLORS.white[50],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.6,
    shadowRadius: 8,
    elevation: 12,
  },
  clusterBadgeLarge: {
    position: 'absolute',
    top: -10,
    right: -10,
    minWidth: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: COLORS.white[50],
  },
  clusterTextLarge: {
    color: COLORS.white[50],
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  clusterPulseLarge: {
    position: 'absolute',
    width: 75,
    height: 75,
    borderRadius: 37.5,
    backgroundColor: 'rgba(69, 183, 209, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(69, 183, 209, 0.4)',
  },

  // Cluster icon container
  clusterIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },

  // Legacy cluster styles (fallback)
  clusterMarker: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primary[200],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: COLORS.white[50],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 5,
    elevation: 8,
  },
  clusterText: {
    color: COLORS.white[50],
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
