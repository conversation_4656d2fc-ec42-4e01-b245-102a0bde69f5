import React, { memo, useCallback, useMemo, forwardRef } from 'react';
import { FlatList, FlatListProps, ViewToken } from 'react-native';

interface OptimizedFlatListProps<T> extends Omit<FlatListProps<T>, 'getItemLayout'> {
  data: T[];
  itemHeight?: number;
  estimatedItemSize?: number;
  enableVirtualization?: boolean;
  onViewableItemsChanged?: (info: { viewableItems: ViewToken[]; changed: ViewToken[] }) => void;
}

/**
 * Performance-optimized FlatList component with best practices built-in
 *
 * Features:
 * - Automatic getItemLayout for known item heights
 * - Optimized rendering settings
 * - Memory management
 * - Performance monitoring
 * - Ref forwarding support
 */
function OptimizedFlatListComponent<T extends { _id?: string; id?: string }>(
  {
    data,
    itemHeight,
    estimatedItemSize = 100,
    enableVirtualization = true,
    onViewableItemsChanged,
    keyExtractor,
    renderItem,
    ...props
  }: OptimizedFlatListProps<T>,
  ref: React.Ref<FlatList<T>>
) {

  // Optimized keyExtractor
  const optimizedKeyExtractor = useCallback((item: T, index: number) => {
    if (keyExtractor) {
      return keyExtractor(item, index);
    }
    // Use stable ID if available, fallback to index
    return item._id || item.id || `item-${index}`;
  }, [keyExtractor]);

  // Optimized getItemLayout for known item heights
  const getItemLayout = useMemo(() => {
    if (!itemHeight) return undefined;

    return (_: any, index: number) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    });
  }, [itemHeight]);

  // Optimized onViewableItemsChanged with throttling
  const throttledOnViewableItemsChanged = useMemo(() => {
    if (!onViewableItemsChanged) return undefined;

    let lastCall = 0;
    const throttleMs = 100; // Throttle to 10fps

    return (info: { viewableItems: ViewToken[]; changed: ViewToken[] }) => {
      const now = Date.now();
      if (now - lastCall >= throttleMs) {
        lastCall = now;
        onViewableItemsChanged(info);
      }
    };
  }, [onViewableItemsChanged]);

  // Performance-optimized render settings
  const performanceProps = useMemo(() => ({
    // Rendering optimization
    initialNumToRender: 10,
    maxToRenderPerBatch: 5,
    windowSize: 10,
    updateCellsBatchingPeriod: 50,

    // Memory optimization
    removeClippedSubviews: enableVirtualization,

    // Scroll optimization
    onEndReachedThreshold: 0.2,

    // Viewability config for better performance
    viewabilityConfig: {
      itemVisiblePercentThreshold: 50,
      minimumViewTime: 100,
    },
  }), [enableVirtualization]);

  return (
    <FlatList
      ref={ref}
      data={data}
      keyExtractor={optimizedKeyExtractor}
      getItemLayout={getItemLayout}
      onViewableItemsChanged={throttledOnViewableItemsChanged}
      renderItem={renderItem}
      {...performanceProps}
      {...props}
    />
  );
}

// Create the forwarded ref component with proper generic typing
const OptimizedFlatList = memo(forwardRef(OptimizedFlatListComponent)) as <T extends { _id?: string; id?: string }>(
  props: OptimizedFlatListProps<T> & { ref?: React.Ref<FlatList<T>> }
) => JSX.Element;

export default OptimizedFlatList;
