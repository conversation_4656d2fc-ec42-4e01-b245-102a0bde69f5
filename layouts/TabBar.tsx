import { COLORS, normalized } from '@/constants/Theme';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React, { forwardRef } from 'react';
import { TouchableOpacity, View, StyleSheet, Platform } from 'react-native';
// Tab bar icons
import { TAB_ICONS } from '@/constants/Images';

// Route names
type Icons = 'index' | 'socialCircles' | 'activities' | 'messages' | 'profile';
const _ICON_SIZE = Platform.OS === 'ios' ? 22.2 : 21;

const IconsRender = (title: Icons, isFocused: boolean) => {
  switch (title) {
    case 'index':
      return isFocused ? (
        <TAB_ICONS.HOME.filled
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      ) : (
        <TAB_ICONS.HOME.outLined
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      );

    case 'activities':
      return isFocused ? (
        <TAB_ICONS.ACTIVITIES.filled
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      ) : (
        <TAB_ICONS.ACTIVITIES.outLined
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      );
    case 'messages':
      return isFocused ? (
        <TAB_ICONS.MYCIRCLES.filled
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      ) : (
        <TAB_ICONS.MYCIRCLES.outLined
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      );
    case 'socialCircles':
      return isFocused ? (
        <TAB_ICONS.CLUBS.filled
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      ) : (
        <TAB_ICONS.CLUBS.outLined
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      );
    case 'profile':
      return isFocused ? (
        <TAB_ICONS.PROFILE.filled
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      ) : (
        <TAB_ICONS.PROFILE.outLined
          width={normalized(_ICON_SIZE)}
          height={normalized(_ICON_SIZE)}
        />
      );
    default:
      break;
  }
};
// turn svg to icon
const renderIcons = ({
  title,
  isFocused,
}: {
  title: Icons;
  isFocused: boolean;
}) => {
  return (
    <View className={` px-4 py-2 justify-center items-center rounded-2xl `}>
      {IconsRender(title, isFocused)}
    </View>
  );
};
const TAB_ORDER: Icons[] = [
  'index',
  'activities',
  'socialCircles',
  'messages',
  'profile',
];

const TabBar = forwardRef((props: BottomTabBarProps, ref) => {
  return (
    <View style={Platform.OS === 'ios' ? styles.tabBar : styles.androidTabBar}>
      {TAB_ORDER.map((tab) => {
        const routeIndex = props.state.routes.findIndex(
          (route) => route.name === tab,
        );
        if (routeIndex === -1) return null; // Skip if route is not found

        const route = props.state.routes[routeIndex];
        const { options } = props.descriptors[route.key];
        const isFocused = props.state.index === routeIndex;

        return (
          <TouchableOpacity
            ref={ref as any}
            key={route.name}
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarButtonTestID}
            onPress={() => props.navigation.navigate(route.name)}
          >
            <View
              style={{
                backgroundColor: isFocused ? COLORS.primary[50] : COLORS.black,
                borderRadius: normalized(20),
                paddingHorizontal: normalized(4.2),
              }}
            >
              {renderIcons({
                title: tab,
                isFocused,
              })}
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
});

const styles = StyleSheet.create({
  tabBar: {
    marginHorizontal: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    bottom: normalized(14),
    position: 'absolute',
    backgroundColor: COLORS.black,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    alignSelf: 'center',
  },
  androidTabBar: {
    height: normalized(55),
    width: '100%',
    marginHorizontal: normalized(8),
    paddingHorizontal: normalized(15),
    backgroundColor: COLORS.black,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    alignSelf: 'center',
  },
});

export default TabBar;
