import {
  useContext,
  createContext,
  type PropsWithChildren,
  useState,
  useEffect,
  useCallback,
} from 'react';
import { useRouter } from 'expo-router';
import { LoginByGoogleAPI } from '@/services/userAPI';

import {
  getToken,
  removeToken,
  setToken,
  isTokenValid,
} from '@/utils/AuthSecureStore';
import { getUserId, removeUserId, setUserId } from '@/utils/HandleSecureStore';
import { IUser } from '@/types';
import { handleError } from '@/utils/errorHandler';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { customToast } from '@/hooks/useCustomToast';
import useLoginByEmail from '@/hooks/userHooks/useLoginByEmail';
import useLocation from '@/hooks/useLocation';
import useUpdateLocation from '@/hooks/userHooks/useUpdateLocation';
import useInitializeBlockedUsers from '@/hooks/userHooks/useInitializeBlockedUsers';

// Initialize Google Config

const AuthContext = createContext<{
  signIn: (credentials: { email: string; password: string }) => void;
  signInWithGoogle: () => Promise<void>;
  signOut: () => void;
  clearAuthData: () => Promise<void>;
  session?: string | null;
  isPending: boolean;
  isGoogleSignInPending: boolean;
  userId?: string | null;
  user: IUser | null;
  setUser: React.Dispatch<React.SetStateAction<IUser | null>>;
  loginErrors: { [key: string]: string | undefined };
  updateUserLocation: () => void;
}>({
  signIn: () => null,
  signInWithGoogle: async () => {},
  signOut: () => null,
  clearAuthData: async () => {},
  session: null,
  isPending: false,
  isGoogleSignInPending: false,
  userId: null,
  user: null,
  setUser: () => null,
  loginErrors: {},
  updateUserLocation: () => null,
});

export function useSession() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
}

export function SessionProvider({ children }: PropsWithChildren) {
  const router = useRouter();
  const [session, setSession] = useState<string | null>(null);
  const [userId, setUserIdState] = useState<string | null>(null);
  const [user, setUser] = useState<IUser | null>(null);

  const [loginErrors, setLoginErrors] = useState<{
    [key: string]: string | undefined;
  }>({});

  // Authentication loading state
  const [isAuthPending, setIsAuthPending] = useState(true);
  const [isGoogleSignInPending, setIsGoogleSignInPending] = useState(false);

  // Location hooks
  const { location: deviceLocation } = useLocation();
  const { mutate: updateLocation } = useUpdateLocation();

  // Initialize blocked users when user is authenticated
  useInitializeBlockedUsers();

  // Login mutation
  const { mutate, isPending: isLoginPending } = useLoginByEmail();

  // Update user location when device location changes and user is logged in
  const updateUserLocation = useCallback(() => {
    if (user && deviceLocation) {
      updateLocation({
        latitude: deviceLocation.latitude,
        longitude: deviceLocation.longitude,
        address: deviceLocation.fullAddress,
      });
    }
  }, [user, deviceLocation, updateLocation]);

  // Update location when user logs in and device location is available
  useEffect(() => {
    if (!user?.address && deviceLocation && user) {
      console.log('user location updated from context');
      updateUserLocation();
    }
  }, [user?._id, deviceLocation?.latitude, deviceLocation?.longitude]);

  const signIn = async (credentials: { email: string; password: string }) => {
    mutate(credentials, {
      onSuccess: (data) => {
        if (data?.user) {
          setUserId(data.user._id);
          setToken(data.token || '');
          setUser(data.user);
          setSession(data.token);
          setUserIdState(data.user._id);
          router.replace('/(protected)/(tabs)');

          // Update location after successful login
          if (deviceLocation) {
            setTimeout(() => {
              updateLocation({
                latitude: deviceLocation.latitude,
                longitude: deviceLocation.longitude,
                address: deviceLocation.fullAddress,
              });
            }, 1000); // Small delay to ensure auth is complete
          }
        }
      },
      onError: (error: any) => {
        console.error(error.response?.data?.message);
        console.log(error);
        customToast(error.response?.data?.message, 'error');
        handleError(error);
      },
    });
  };

  /**
   * Sign in with Google
   * Handles the Google authentication flow and API integration
   */

  const signInWithGoogle = async () => {
    try {
      setIsGoogleSignInPending(true);

      // Check if Google Play Services are available (Android only)
      await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });

      // Sign out first to ensure a fresh sign-in attempt
      await GoogleSignin.signOut();

      // Start the Google sign-in flow
      const userInfo = await GoogleSignin.signIn();

      if (!userInfo.data?.idToken) {
        customToast('Failed to get authentication token from Google', 'error');
        return;
      }

      // Call our backend API with the Google ID token
      const response = await LoginByGoogleAPI({
        idToken: userInfo?.data.idToken,
      });

      // Handle successful login
      if (response?.token && response?.user?._id) {
        // Store the token and user ID
        setToken(response.token);
        setUserId(response.user._id);

        // Update the auth context state
        setSession(response.token);
        setUserIdState(response.user._id);
        setUser(response.user);

        // Show success message
        customToast('Successfully signed in with Google', 'success');

        router.replace('/(protected)/(tabs)');

        // Update location after successful Google login
        if (deviceLocation) {
          setTimeout(() => {
            updateLocation({
              latitude: deviceLocation.latitude,
              longitude: deviceLocation.longitude,
              address: deviceLocation.fullAddress,
            });
          }, 1000); // Small delay to ensure auth is complete
        }
      } else {
        customToast('Invalid response from server', 'error');
      }
    } catch (error: any) {
      console.error('Google sign-in error:', error);

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // User cancelled the sign-in flow
        customToast('Sign in was cancelled', 'info');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        customToast('Sign in is already in progress', 'info');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        customToast('Play services not available or outdated', 'error');
      } else if (error.code === 'DEVELOPER_ERROR') {
        console.error('DEVELOPER_ERROR: OAuth configuration is incorrect');
        customToast(
          'Google Sign-In configuration error. Please contact support.',
          'error',
        );
      } else if (error?.response?.status === 404) {
        // User doesn't exist - redirect to registration
        customToast('Account not found. Please register first.', 'info');
        router.push('/auth/register');
      } else if (
        error?.response?.status === 403 &&
        error?.response?.data?.user?.userStatus === 'unverified'
      ) {
        // Handle unverified user case
        customToast('Please verify your account to continue', 'info');
        router.push({
          pathname: '/auth/otp',
          params: {
            email: error?.response?.data?.user?.email,
            userId: error?.response?.data?.user?._id,
            type: 'verify',
          },
        });
      } else {
        // Generic error message
        customToast(
          error?.response?.data?.message || 'Failed to sign in with Google',
          'error',
        );
      }
    } finally {
      setIsGoogleSignInPending(false);
    }
  };

  /**
   * Sign out the current user and clear all auth data
   */

  const signOut = async () => {
    try {
      // Just sign out from Google without checking if signed in
      // This is safer and handles the case where the user might be signed in
      await GoogleSignin.signOut();
    } catch (error) {
      console.error('Error signing out from Google:', error);
    }

    // Clear local auth data
    await removeToken();
    await removeUserId();
    setSession(null);
    setUserIdState(null);
    setUser(null);

    // Navigate to auth screen
    router.replace('/auth');
  };

  /**
   * Clear all auth data without navigation
   * Useful for handling invalid sessions
   */

  const clearAuthData = async () => {
    // Sign out from Google if signed in with Google
    try {
      // Just sign out from Google without checking if signed in
      await GoogleSignin.signOut();
    } catch (error) {
      console.error('Error signing out from Google:', error);
    }

    // Clear local auth data
    await removeToken();
    await removeUserId();
    setSession(null);
    setUserIdState(null);
    setUser(null);
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = await getToken();
        const userId = await getUserId();
        const isValid = await isTokenValid();

        if (token && userId && isValid) {
          // User is logged in with a valid token
          setSession(token);
          setUserIdState(userId);
        } else {
          // No valid session found
          console.log('No valid session found or token is invalid');
          // Clear any existing invalid session data
          if (token && !isValid) {
            console.log('Token is invalid, clearing session');
            await clearAuthData();
          } else {
            // Just clear the session state without removing tokens
            setSession(null);
            setUserIdState(null);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Clear all auth data on error
        await clearAuthData();
      } finally {
        // Always set isAuthPending to false when auth check is complete
        setIsAuthPending(false);
      }
    };

    initializeAuth();
  }, [router]);

  return (
    <AuthContext.Provider
      value={{
        signIn,
        signInWithGoogle,
        signOut,
        clearAuthData,
        session,
        isPending: isLoginPending || isAuthPending,
        isGoogleSignInPending,
        userId,
        user,
        setUser,
        loginErrors,
        updateUserLocation,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
