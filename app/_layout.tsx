import 'react-native-reanimated';
import '../global.css';
import '@/i18n';
import 'react-native-get-random-values';

import SplashScreen from './splash';
import Toast from 'react-native-toast-message';
import { StatusBar } from 'expo-status-bar';
import { Slot } from 'expo-router';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SessionProvider } from '@/context/AuthContext';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NotificationProvider } from '@/context/NotificationsContext';
import { SocketProvider } from '@/context/SocketContext';
import { useFonts } from 'expo-font';
import { ToastComponent } from '@/components/ui/Toast/ToastComponent';
import { useEffect } from 'react';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import * as WebBrowser from 'expo-web-browser';

// Initialize React Query client with optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      staleTime: 5 * 60 * 1000, // 5 minutes default stale time
      gcTime: 10 * 60 * 1000, // 10 minutes cache time (formerly cacheTime)
      // Enable background refetching for better UX
      refetchOnMount: 'always',
      // Network mode for better offline handling
      networkMode: 'online',
    },
    mutations: {
      retry: 1,
      networkMode: 'online',
    },
  },
});

WebBrowser.maybeCompleteAuthSession();

// Root layout component with initialization logic
export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    'poppins-light': require('../assets/fonts/Poppins-Light.ttf'),
    'poppins-regular': require('../assets/fonts/Poppins-Regular.ttf'),
    'poppins-semibold': require('../assets/fonts/Poppins-SemiBold.ttf'),
    'poppins-bold': require('../assets/fonts/Poppins-Bold.ttf'),
  });

  // Configure Google Sign-In early in the app lifecycle
  useEffect(() => {
    GoogleSignin.configure({
      webClientId: process.env.EXPO_PUBLIC_GOOGLE_SIGN_IN_WEB_CLIENT_ID,
      iosClientId: process.env.EXPO_PUBLIC_GOOGLE_SIGN_IN_IOS_CLIENT_ID,
      offlineAccess: true,
      profileImageSize: 150,
    });
  }, []);

  // Show splash screen while loading
  if (!fontsLoaded) {
    return <SplashScreen />;
  }

  return (
    <GestureHandlerRootView className='flex-1'>
      <QueryClientProvider client={queryClient}>
        <SessionProvider>
          <SocketProvider>
            <NotificationProvider>
              <Slot />
              <StatusBar style='light' />

              {/* Toast to show with the custom design */}
              <Toast
                position='top'
                topOffset={50}
                visibilityTime={4000}
                config={{
                  success: ({ text1, ...rest }) => (
                    <ToastComponent text1={text1 || ''} type='success' />
                  ),
                  error: ({ text1, ...rest }) => (
                    <ToastComponent type='error' text1={text1 || ''} />
                  ),
                  info: ({ text1, ...rest }) => (
                    <ToastComponent type='info' text1={text1 || ''} />
                  ),
                }}
              />
            </NotificationProvider>
          </SocketProvider>
        </SessionProvider>
      </QueryClientProvider>
    </GestureHandlerRootView>
  );
}
