import React, { useCallback, useMemo, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useLocalSearchParams } from 'expo-router';

// Components
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { COLORS } from '@/constants/Theme';
import PostsList from '@/components/ProfileComponents/PostsList';
import MediaList from '@/components/ProfileComponents/MediaList';
import ProfileHeader from '@/components/ProfileComponents/ProfileHeader';
import ProfileFollowersAndFollowing from '@/components/ProfileComponents/ProfileFollowersAndFollowing';
import ProfileFilterButtons, {
  FilterId,
} from '@/components/ProfileComponents/profileFilterButtons';
import ProfileFollowingActions from '@/components/ProfileComponents/ProfileFollowingActions';
import CustomModalGallery from '@/components/ui/ModalGalleryList';

// Hooks
import { useSession } from '@/context/AuthContext';
import useGetUserDataByID from '@/hooks/userHooks/useGetUserDataByID';
import useGetPostsByUserID from '@/hooks/profileHooks/useGetPostByUserId';
import useGetMediaByUserId from '@/hooks/profileHooks/useGetMediaByUserId';
import useGetUserLikes from '@/hooks/profileHooks/useGetUserLikes';
import useGetBlockedUsers from '@/hooks/userHooks/useGetBlockedUsers';

const UserProfile = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { userId } = useSession();

  // Determine which user profile to show - current user or requested user
  const currentId = useMemo(() => {
    if (!id && userId) {
      return userId;
    }
    return id || null;
  }, [id, userId]);

  // Fetch user data
  const { data: userData, isLoading: userLoading } = useGetUserDataByID(
    currentId || '',
    {
      queryKey: ['userData', currentId],
      enabled: !!currentId,
    },
  );

  const user = userData?.user;

  // Determine if viewing own profile
  const isCurrentUser = useMemo(() => {
    return userId === user?._id;
  }, [userId, user?._id]);

  // Initialize blocked users data when viewing own profile
  useGetBlockedUsers({
    enabled: isCurrentUser && !!userId,
  });

  // UI state
  const [selectedFilter, setSelectedFilter] = useState<FilterId>('posts');
  const [selectImages, setSelectedImages] = useState<null | string[]>(null);
  const [galleryModal, setGalleryModal] = useState(false);

  // Fetch posts data - only when posts filter is selected
  const { data: postsData, isLoading: postsLoading } = useGetPostsByUserID(
    currentId || '',
    {
      enabled: selectedFilter === 'posts' && !!currentId,
    },
  );

  // Fetch likes data - only when likes filter is selected
  const { data: likedData, isLoading: likesLoading } = useGetUserLikes(
    userId!,
    {
      enabled: selectedFilter === 'likes' && !!currentId && isCurrentUser,
    },
  );

  // Fetch media data with pagination - only when media filter is selected
  const {
    data: media,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: mediaLoading,
    isError,
    refetch: refetchMedia,
  } = useGetMediaByUserId(
    { userId: currentId || '', limit: 10 },
    {
      enabled: selectedFilter === 'media' && !!currentId,
      initialPageParam: { userId: currentId, limit: 10 },
    },
  );

  // Process media data
  const mediaData = useMemo(() => {
    return media?.pages?.flatMap((page) => page.data) ?? [];
  }, [media?.pages]);

  // Determine if content is loading based on selected filter
  const isContentLoading = useMemo(() => {
    if (selectedFilter === 'posts') return postsLoading;
    if (selectedFilter === 'media') return mediaLoading;
    if (selectedFilter === 'likes') return likesLoading;
    return false;
  }, [selectedFilter, postsLoading, mediaLoading, likesLoading]);

  // Callbacks
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !mediaLoading) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, mediaLoading, fetchNextPage]);

  const handleSelectImage = useCallback((url: string[]) => {
    setSelectedImages(url);
    setGalleryModal(true);
  }, []);

  const handleChangeFilter = useCallback((e: FilterId) => {
    setSelectedFilter(e);
  }, []);

  return (
    <>
      <CustomModalGallery
        images={selectImages || []}
        onClose={() => setGalleryModal(false)}
        visible={galleryModal}
      />

      <ScreenTemplate>
        {userLoading ? (
          <View className='flex-1 justify-center items-center'>
            <ActivityIndicator size='large' color={COLORS.secondary[300]} />
          </View>
        ) : (
          /* Header */
          <View className='px-2 flex-1'>
            <ProfileHeader
              // isCurrentUser={isCurrentUser}
              avatar={user?.image || ''}
              displayName={user?.displayName || ''}
              location={user?.address || ''}
              username={user?.username || ''}
              userId={user?._id}
            />
            {!isCurrentUser && (
              <ProfileFollowingActions
                isSameUser={isCurrentUser}
                userId={user?._id || ''}
                userInfo={user}
              />
            )}
            <ProfileFollowersAndFollowing
              followers={Number(user?.followersCount || 0)}
              following={Number(user?.followingCount || 0)}
              userId={user?._id || ''}
            />
            <ProfileFilterButtons
              onChange={handleChangeFilter}
              value={selectedFilter}
            />

            {/* Content Loading Indicator */}
            {isContentLoading ? (
              <View className='flex-1 justify-center items-center py-10'>
                <ActivityIndicator size='large' color={COLORS.secondary[300]} />
              </View>
            ) : (
              <>
                {/* Posts Lists */}
                {selectedFilter === 'posts' && (
                  <PostsList
                    data={postsData?.posts}
                    userId={user?._id || ''}
                    isLoading={false}
                  />
                )}

                {/* Media Lists */}
                {selectedFilter === 'media' && (
                  <MediaList
                    onPress={handleSelectImage}
                    data={mediaData}
                    handleLoadMore={handleLoadMore}
                    isFetchingNextPage={isFetchingNextPage}
                    isError={isError}
                    isLoading={false}
                    refetch={refetchMedia}
                  />
                )}

                {/* Likes List */}
                {selectedFilter === 'likes' && (
                  <PostsList
                    data={likedData?.data || []}
                    userId={user?._id || ''}
                    isLoading={false}
                  />
                )}
              </>
            )}
          </View>
        )}
      </ScreenTemplate>
    </>
  );
};

export default UserProfile;
