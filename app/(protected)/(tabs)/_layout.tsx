import { Tabs } from 'expo-router';
import { Platform } from 'react-native';
import TabBar from '@/layouts/TabBar';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        // Enable lazy loading for better performance
        lazy: true,
        // Optimize for Android performance
        ...(Platform.OS === 'android' && {
          // Reduce animation duration on Android
          animationEnabled: true,
          // Use native driver for better performance
          gestureEnabled: true,
          // Optimize memory usage
          unmountOnBlur: false, // Keep screens mounted for faster switching
        }),
      }}
      tabBar={(props) => <TabBar {...props} />}
    >
      <Tabs.Screen
        name='index'
        options={{
          // Preload this screen as it's the default
          lazy: false,
        }}
      />
      <Tabs.Screen
        name='activities'
        options={{
          lazy: true,
        }}
      />
      <Tabs.Screen
        name='socialCircles'
        options={{
          lazy: true,
        }}
      />
      <Tabs.Screen
        name='messages'
        options={{
          lazy: true,
        }}
      />
      <Tabs.Screen
        name='profile'
        options={{
          lazy: true,
        }}
      />
    </Tabs>
  );
}
