import { Tabs } from 'expo-router';
import TabBar from '@/layouts/TabBar';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{ headerShown: false }}
      tabBar={(props) => <TabBar {...props} />}
    >
      <Tabs.Screen name='index' />
      <Tabs.Screen name='activities' />
      <Tabs.Screen name='socialCircles' />
      <Tabs.Screen name='messages' />
      <Tabs.Screen name='profile' />
    </Tabs>
  );
}
