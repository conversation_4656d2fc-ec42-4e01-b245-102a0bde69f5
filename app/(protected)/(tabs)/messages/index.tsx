import React, { memo, useCallback } from 'react';
import { View } from 'react-native';

// Custom hooks for separated logic
import { useMessagesData } from '@/hooks/chatHooks/useMessagesData';
import { useMessagesNavigation } from '@/hooks/chatHooks/useMessagesNavigation';
import { useMessagesLayout } from '@/hooks/useMessagesLayout';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

// Optimized components
import CreateGroupButton from '@/components/MessagesComponents/CreateGroupButton';
import ChatListItemOptimized from '@/components/MessagesComponents/ChatListItemOptimized';
import EmptyMessagesComponent from '@/components/MessagesComponents/EmptyMessagesComponent';
import OptimizedFlatList from '@/components/ui/OptimizedFlatList';

/**
 * Optimized MessagesScreen component with separated concerns
 * Uses custom hooks for logic separation and memoized components for performance
 */
const MessagesScreen = () => {
  // Performance monitoring
  const { measureSync } = usePerformanceMonitor({
    componentName: 'MessagesScreen',
    logSlowRenders: true,
  });

  // Extract messages data and logic using custom hook
  const {
    filteredChats,
    isLoading,
    hasChats,
    refreshChats,
  } = useMessagesData();

  // Extract navigation logic using custom hook
  const { navigateToCreateGroup, navigateToChat } = useMessagesNavigation();

  // Extract layout calculations using custom hook
  const { listClassName } = useMessagesLayout();

  // Memoized render item function
  const renderItem = useCallback(({ item }: { item: any }) => (
    <ChatListItemOptimized
      item={item}
      onPress={navigateToChat}
    />
  ), [navigateToChat]);

  // Memoized key extractor
  const keyExtractor = useCallback((item: any) => item.user_id || item._id, []);

  // Memoized separator component
  const ItemSeparatorComponent = useCallback(() => (
    <View className='border-hairline border-neutral-300/20 mt-3' />
  ), []);

  // Memoized empty component
  const ListEmptyComponent = useCallback(() => (
    <EmptyMessagesComponent />
  ), []);

  return (
    <View className='flex-1 px-2'>
      {/* Create Group Button */}
      <CreateGroupButton onPress={navigateToCreateGroup} />

      {/* Chat List */}
      <View className='flex-1 pt-4'>
        <OptimizedFlatList
          data={filteredChats}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
          contentContainerClassName='gap-5'
          className={listClassName}
          itemHeight={80} // Estimated height for better performance
          enableVirtualization={true}
          ItemSeparatorComponent={ItemSeparatorComponent}
          ListEmptyComponent={ListEmptyComponent}
          refreshing={isLoading}
          onRefresh={refreshChats}
          // Performance optimizations
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          windowSize={10}
        />
      </View>
    </View>
  );
};

export default MessagesScreen;
