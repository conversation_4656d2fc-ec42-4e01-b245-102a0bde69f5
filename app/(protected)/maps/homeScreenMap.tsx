import {
  Pressable,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import MapView, {
  Callout,
  Marker,
  PROVIDER_GOOGLE,
  Region,
} from 'react-native-maps';
import { isPointCluster, useClusterer } from 'react-native-clusterer';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { COLORS, SCREEN_HEIGHT, SCREEN_WIDTH } from '@/constants/Theme';
import { Image } from 'expo-image';
import useLocation from '@/hooks/useLocation';
import CustomText from '@/components/ui/CustomText';
import GoBack from '@/layouts/GoBack';
import GooglePlacesInput from '@/components/ui/GooglePlacesInput';
import { APP_Icons } from '@/constants/Images';
import { ILocation } from '@/types';
import useGetAllActivities from '@/hooks/activityHooks/useGetAllActivities';
import { router } from 'expo-router';
import { BlurView } from 'expo-blur';
import { useSession } from '@/context/AuthContext';
import { Ionicons } from '@expo/vector-icons';

// Constants
const _circleSize = 55.9;
const _userCircle = 25;
const _circleArrowStroke = 8;
const _userArrowStroke = 5;

// Map optimization constants
const MAP_OPTIMIZATION = {
  maxZoomLevel: 18,
  minZoomLevel: 3,
  clusterRadius: 50,
  minClusterSize: 2,
  loadingIndicatorColor: COLORS.primary[200],
  loadingBackgroundColor: 'rgba(255, 255, 255, 0.7)',
};

const InputRow = memo(
  ({ title, address }: { title: string; address: string }) => (
    <View className='flex-row gap-2  w-full h-[100%]   '>
      <CustomText className='text-black font-bold '>{title}</CustomText>
      <CustomText className='text-black font-bold '>{address}</CustomText>
    </View>
  ),
);

const HomeScreenMap = () => {
  const { data } = useGetAllActivities({});
  const { user } = useSession();
  const { location: userLocation } = useLocation();
  const inset = useSafeAreaInsets();

  const mapRef = useRef<MapView>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize region with default location and user location fallback
  const [region, setRegion] = useState<Region>(() => ({
    latitude: userLocation?.latitude || DEFAULT_LOCATION.latitude,
    longitude: userLocation?.longitude || DEFAULT_LOCATION.longitude,
    latitudeDelta: DEFAULT_LOCATION.latitudeDelta,
    longitudeDelta: DEFAULT_LOCATION.longitudeDelta,
  }));

  const [location, setLocation] = useState<ILocation>({
    latitude: user?.location?.latitude || 0,
    longitude: user?.location?.longitude || 0,
    address: user?.address,
  });

  // Memoize the points data with proper validation and debugging
  const pointsData = useMemo(() => {
    if (!data?.pages) {
      return [];
    }

    const points = data.pages.flatMap((page) =>
      page.data
        .filter((activity) => {
          // Validate activity has proper location data
          return (
            activity.location &&
            typeof activity.location.latitude === 'number' &&
            typeof activity.location.longitude === 'number' &&
            !isNaN(activity.location.latitude) &&
            !isNaN(activity.location.longitude) &&
            activity.location.latitude !== 0 &&
            activity.location.longitude !== 0
          );
        })
        .map((activity) => ({
          type: 'Feature' as const,
          geometry: {
            type: 'Point' as const,
            coordinates: [
              activity.location.longitude,
              activity.location.latitude,
            ],
          },
          properties: {
            ...activity,
            cluster: false,
          },
        })),
    );

    console.log(`📍 Created ${points.length} valid points for clustering`);
    return points;
  }, [data?.pages]);

  // Use clusterer with optimized configuration
  const [points, superClusterer] = useClusterer(
    pointsData,
    {
      height: SCREEN_HEIGHT,
      width: SCREEN_WIDTH,
    },
    region,
    {
      radius: MAP_OPTIMIZATION.clusterRadius,
      minPoints: MAP_OPTIMIZATION.minClusterSize,
      maxZoom: 16,
    },
  );

  // Handle region change with debounce
  const handleRegionChange = useCallback((newRegion: Region) => {
    setRegion(newRegion);
  }, []);

  // Handle "My Location" button press
  const handleMyLocationPress = useCallback(() => {
    if (userLocation && userLocation.latitude && userLocation.longitude) {
      const newRegion = {
        latitude: userLocation.latitude,
        longitude: userLocation.longitude,
        latitudeDelta: 0.01, // Closer zoom for "my location"
        longitudeDelta: 0.01,
      };
      mapRef.current?.animateToRegion(newRegion, 1000);
    }
  }, [userLocation]);

  // Debug clustering data
  useEffect(() => {
    console.log('📍 Clustering Debug Info:', {
      totalActivities: data?.pages?.flatMap((p) => p.data).length || 0,
      validPoints: pointsData.length,
      clusteredPoints: points.length,
      region: region,
      mapDimensions: { width: SCREEN_WIDTH, height: SCREEN_HEIGHT },
    });
  }, [data, pointsData, points, region]);

  // Update region and location when user location is available
  useEffect(() => {
    if (
      userLocation &&
      typeof userLocation.latitude === 'number' &&
      typeof userLocation.longitude === 'number' &&
      !isNaN(userLocation.latitude) &&
      !isNaN(userLocation.longitude) &&
      userLocation.latitude !== 0 &&
      userLocation.longitude !== 0
    ) {
      // Update the region to center on user location
      const newRegion = {
        latitude: userLocation.latitude,
        longitude: userLocation.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      };

      setRegion(newRegion);
      setLocation({
        latitude: userLocation.latitude,
        longitude: userLocation.longitude,
        address: userLocation.fullAddress || '',
      });

      // Animate to user location on first load (only once)
      setTimeout(() => {
        mapRef.current?.animateToRegion(newRegion, 1000);
      }, 500);
    } else {
    }
  }, [userLocation?.latitude, userLocation?.longitude]);

  // Animate to location when it changes (with debounce)
  useEffect(() => {
    if (location && location.latitude && location.longitude) {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout for animation
      timeoutRef.current = setTimeout(() => {
        mapRef.current?.animateCamera(
          {
            center: {
              latitude: location.latitude,
              longitude: location.longitude,
            },
            zoom: 10, // Increased zoom for better focus
          },
          { duration: 500 },
        );
      }, 300); // Reduced delay for better responsiveness
    }

    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [location.latitude, location.longitude]); // Only run when coordinates change

  return (
    <SafeAreaView className='flex-1'>
      {/* Header and GoBack  */}
      <View
        className='absolute w-full z-10 px-2 gap-2'
        style={{ marginTop: inset.top }}
      >
        <GoBack iconColor={COLORS.neutral[800]} />

        <View className=' flex-1 w-full   py-3 px-2.5 rounded-xl  flex-row items-center'>
          <BlurView style={{ flex: 1 }} intensity={20} tint='light'>
            <GooglePlacesInput
              predefinedPlaces={[]}
              fetchDetails={true}
              renderLeftButton={() => (
                <View className='justify-center mx-2'>
                  <APP_Icons.SearchIcon width={24} height={24} />
                </View>
              )}
              textInputProps={{
                placeholderTextColor: COLORS.neutral[900],
                backgroundColor: 'transparent',
                fontWeight: '600',
              }}
              isRowScrollable={true}
              renderRow={(rowData) => {
                const title = rowData.structured_formatting.main_text;
                const address = rowData.structured_formatting.secondary_text;
                return <InputRow address={address} title={title} />;
              }}
              styles={{
                row: styles.row,
                container: styles.container,
                listView: styles.listView,
              }}
              debounce={500}
              keyboardShouldPersistTaps='always'
              enablePoweredByContainer={false}
              onPress={(data, details) => {
                if (!details) return;

                setLocation({
                  address: data.description,
                  latitude: details.geometry.location.lat,
                  longitude: details.geometry.location.lng,
                });
              }}
            />
          </BlurView>
        </View>
      </View>

      {/* Map View */}
      <MapView
        provider={PROVIDER_GOOGLE}
        style={StyleSheet.absoluteFillObject}
        ref={mapRef}
        onRegionChangeComplete={handleRegionChange}
        initialRegion={region}
        cameraZoomRange={{
          animated: true,
          maxCenterCoordinateDistance: 20,
          minCenterCoordinateDistance: 10,
        }}
        rotateEnabled={false}
        loadingEnabled={true}
        loadingIndicatorColor={MAP_OPTIMIZATION.loadingIndicatorColor}
        loadingBackgroundColor={MAP_OPTIMIZATION.loadingBackgroundColor}
        showsMyLocationButton={false}
        userLocationPriority='high'
        userLocationUpdateInterval={5000}
        userLocationFastestInterval={2000}
        showsCompass={false}
        showsScale={false}
        showsBuildings={true}
        showsTraffic={false}
        showsIndoors={true}
        // Performance optimizations
        moveOnMarkerPress={false}
        pitchEnabled={false}
        scrollEnabled={true}
        zoomEnabled={true}
        zoomTapEnabled={true}
        zoomControlEnabled={false}
      >
        {/* Custom user location button with better visibility */}
        {userLocation &&
          userLocation.latitude &&
          userLocation.longitude &&
          userLocation.latitude !== 0 &&
          userLocation.longitude !== 0 && (
            <Marker
              coordinate={{
                latitude: userLocation.latitude,
                longitude: userLocation.longitude,
              }}
              anchor={{ x: 0.5, y: 0.5 }}
              tracksViewChanges={false}
              zIndex={1000}
            />
          )}

        {/* Render markers and clusters with enhanced error handling */}
        {useMemo(() => {
          if (!points || points.length === 0) {
            console.log('📍 No points to render');
            return null;
          }

          return points
            .map((point, index) => {
              try {
                const isCluster = isPointCluster(point);
                const coords = point.geometry.coordinates;
                const lat = coords[1];
                const lng = coords[0];

                // Validate coordinates
                if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
                  console.warn(
                    `📍 Invalid coordinates for point ${index}:`,
                    coords,
                  );
                  return null;
                }

                if (isCluster) {
                  // Enhanced cluster marker
                  const pointCount = point.properties.point_count || 0;
                  console.log(`📍 Rendering cluster with ${pointCount} points`);

                  // Determine cluster size for dynamic styling
                  const getClusterSize = (count: number) => {
                    if (count >= 100) return 'large';
                    if (count >= 10) return 'medium';
                    return 'small';
                  };

                  const clusterSize = getClusterSize(pointCount);

                  // Get styles based on cluster size
                  const getClusterStyles = (size: string) => {
                    switch (size) {
                      case 'large':
                        return {
                          container: styles.clusterLarge,
                          background: styles.clusterBackgroundLarge,
                          badge: styles.clusterBadgeLarge,
                          text: styles.clusterTextLarge,
                          pulse: styles.clusterPulseLarge,
                        };
                      case 'medium':
                        return {
                          container: styles.clusterMedium,
                          background: styles.clusterBackgroundMedium,
                          badge: styles.clusterBadgeMedium,
                          text: styles.clusterTextMedium,
                          pulse: styles.clusterPulseMedium,
                        };
                      default:
                        return {
                          container: styles.clusterSmall,
                          background: styles.clusterBackgroundSmall,
                          badge: styles.clusterBadgeSmall,
                          text: styles.clusterTextSmall,
                          pulse: styles.clusterPulseSmall,
                        };
                    }
                  };

                  const clusterStyles = getClusterStyles(clusterSize);

                  return (
                    <Marker
                      key={`cluster-${point.id || index}`}
                      onPress={() => {
                        try {
                          const region = point.properties.getExpansionRegion();
                          mapRef.current?.animateToRegion(region, 500);
                        } catch (error) {
                          console.error('📍 Error expanding cluster:', error);
                        }
                      }}
                      coordinate={{ latitude: lat, longitude: lng }}
                      tracksViewChanges={false}
                      anchor={{ x: 0.5, y: 0.5 }}
                      zIndex={1000}
                    >
                      <View
                        style={[
                          styles.clusterContainer,
                          clusterStyles.container,
                        ]}
                      >
                        {/* Pulse animation ring (behind everything) */}
                        <View style={clusterStyles.pulse} />

                        {/* Cluster background with gradient effect */}
                        <View style={clusterStyles.background} />

                        {/* Cluster icon/image */}
                        <View style={styles.clusterIconContainer}>
                          <Ionicons
                            name='location'
                            size={
                              clusterSize === 'large'
                                ? 20
                                : clusterSize === 'medium'
                                  ? 16
                                  : 12
                            }
                            color={COLORS.white[50]}
                          />
                        </View>

                        {/* Count badge */}
                        <View style={clusterStyles.badge}>
                          <CustomText style={clusterStyles.text}>
                            {pointCount}
                          </CustomText>
                        </View>
                      </View>
                    </Marker>
                  );
                } else {
                  // Individual activity marker
                  console.log(
                    `📍 Rendering individual marker: ${point.properties.name}`,
                  );
                  return (
                    <MemoizedMarker
                      key={`point-${point.properties._id || index}`}
                      id={point.properties._id || `activity-${index}`}
                      image={point.properties.images?.[0] || ''}
                      name={point.properties.name || 'Activity'}
                      latitude={lat}
                      longitude={lng}
                    />
                  );
                }
              } catch (error) {
                console.error(`📍 Error rendering point ${index}:`, error);
                return null;
              }
            })
            .filter(Boolean); // Remove null entries
        }, [points, mapRef])}
      </MapView>

      {/* Location button */}
      <TouchableOpacity
        onPress={handleMyLocationPress}
        className='absolute w-12 h-12 rounded-full bg-white-50 justify-center items-center bottom-20 right-10'
      >
        <APP_Icons.LocationIcon width={26} height={26} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default HomeScreenMap;

// Optimized marker component with useCallback for event handlers
const MemoizedMarker = memo(
  (props: {
    latitude: number;
    longitude: number;
    name: string;
    image: string;
    id: string;
  }) => {
    // Memoize the onPress handler to prevent recreating on each render
    const handlePress = useCallback(() => {
      router.replace(`/(protected)/activity/${props.id}`);
    }, [props.id]);

    return (
      <Marker
        onPress={handlePress}
        coordinate={{ latitude: props.latitude, longitude: props.longitude }}
        tracksViewChanges={false} // Performance optimization
      >
        <View style={styles.markerContainer}>
          <View style={styles.circle}>
            <Image
              source={{ uri: props.image }}
              style={styles.image}
              cachePolicy='memory-disk'
              transition={300}
            />
          </View>
          <View style={styles.arrow} />
        </View>
        <Callout tooltip style={styles.callout}>
          <CustomText numberOfLines={2} ellipsizeMode='tail'>
            {props.name}
          </CustomText>
        </Callout>
      </Marker>
    );
  },
  // Custom equality function to prevent unnecessary re-renders
  (prevProps, nextProps) => {
    return (
      prevProps.id === nextProps.id &&
      prevProps.latitude === nextProps.latitude &&
      prevProps.longitude === nextProps.longitude &&
      prevProps.image === nextProps.image
    );
  },
);

// Optimized styles with better organization and performance considerations
const styles = StyleSheet.create({
  // Map marker styles
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
    backgroundColor: '#DDD', // Placeholder color while loading
  },
  circle: {
    width: _circleSize,
    height: _circleSize,
    borderRadius: _circleSize / 2,
    backgroundColor: COLORS.primary[200],
    overflow: 'hidden',
    borderWidth: 5,
    borderColor: COLORS.primary[200],
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  arrow: {
    width: 0,
    height: 0,
    borderLeftWidth: _circleArrowStroke,
    borderRightWidth: _circleArrowStroke,
    borderTopWidth: _circleArrowStroke * 2,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: COLORS.primary[200],
    marginTop: -_circleArrowStroke + 6,
  },

  // User location marker styles - enhanced for better visibility
  userMarker: {
    justifyContent: 'center',
    alignItems: 'center',
    width: _userCircle + 10, // Slightly larger for better visibility
    height: _userCircle + 10,
  },
  userOuterRing: {
    position: 'absolute',
    width: _userCircle + 8,
    height: _userCircle + 8,
    borderRadius: (_userCircle + 8) / 2,
    backgroundColor: 'rgba(66, 165, 245, 0.2)', // Light blue with transparency
    borderWidth: 2,
    borderColor: 'rgba(66, 165, 245, 0.8)', // Stronger blue border
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  userInnerCircle: {
    width: _userCircle / 2,
    height: _userCircle / 2,
    borderRadius: _userCircle / 4,
    backgroundColor: '#42A5F5', // Solid blue color
    borderWidth: 2,
    borderColor: 'white',
    // Add shadow for depth
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },

  // New user location pinpoint styles
  userLocationPinpoint: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userLocationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#1976D2', // Material Blue
    borderWidth: 3,
    borderColor: 'white',
    position: 'absolute',
    zIndex: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  userLocationPulse: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(25, 118, 210, 0.3)', // Semi-transparent blue
    borderWidth: 1,
    borderColor: 'rgba(25, 118, 210, 0.6)',
    position: 'absolute',
    zIndex: 1,
  },



  // Callout styles
  callout: {
    width: 180, // Increased width for better readability
    padding: 10,
    borderRadius: 10,
    backgroundColor: COLORS.white[50],
    justifyContent: 'center',
    alignItems: 'center',
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
  },

  // My Location Button styles
  myLocationButton: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: COLORS.white[50],
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  myLocationButtonPressed: {
    transform: [{ scale: 0.95 }],
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  locationButtonContent: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  locationButtonPulse: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(25, 118, 210, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(25, 118, 210, 0.2)',
    top: -2,
    left: -2,
    zIndex: -1,
  },

  // Google search input styles
  listView: {
    backgroundColor: 'rgba(172, 172, 172, 0.6)',
    borderRadius: 20,
    marginHorizontal: 5,
    marginTop: 5,
    maxHeight: SCREEN_HEIGHT * 0.4, // Limit height to prevent covering the whole screen
  },
  row: {
    backgroundColor: 'rgba(172, 172, 172, 0.6)',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
  },
  container: {
    backgroundColor: 'rgba(172, 172, 172, 0.6)',
    borderWidth: 1,
    borderColor: COLORS.primary[50],
    borderRadius: 20,
    overflow: 'hidden',
  },
});
