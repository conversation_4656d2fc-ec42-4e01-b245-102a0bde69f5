import { Pressable, StyleSheet, View } from 'react-native';
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import MapView, {
  Callout,
  Marker,
  PROVIDER_GOOGLE,
  Region,
} from 'react-native-maps';
import { isPointCluster, useClusterer } from 'react-native-clusterer';

import { COLORS, SCREEN_HEIGHT, SCREEN_WIDTH } from '@/constants/Theme';
import useLocation from '@/hooks/useLocation';
import CustomText from '@/components/ui/CustomText';
import { Image } from 'expo-image';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import GoBack from '@/layouts/GoBack';
import GooglePlacesInput from '@/components/ui/GooglePlacesInput';
import { APP_Icons } from '@/constants/Images';
import { ILocation } from '@/types';
import useGetAllActivities from '@/hooks/activityHooks/useGetAllActivities';
import { router } from 'expo-router';
import { BlurView } from 'expo-blur';

// TODO show current user location - Show activities and attractions

// Constants
const _circleSize = 55.9;
const _userCircle = 25;
const _circleArrowStroke = 8;
const _userArrowStroke = 5;

const InputRow = memo(
  ({ title, address }: { title: string; address: string }) => (
    <View className='flex-row gap-2  w-full h-[100%]   '>
      <CustomText className='text-black font-bold '>{title}</CustomText>
      <CustomText className='text-black font-bold '>{address}</CustomText>
    </View>
  ),
);

const HomeScreenMap = () => {
  const { data } = useGetAllActivities({});
  const { location: userLocation } = useLocation();
  const inset = useSafeAreaInsets();

  const mapRef = useRef<MapView>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize region with memoized default values
  const [region, setRegion] = useState<Region>(() => ({
    latitude: userLocation?.latitude || 40.785091,
    longitude: userLocation?.longitude || -73.968285,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  }));

  const [location, setLocation] = useState<ILocation>({
    latitude: 0,
    longitude: 0,
    address: '',
  });

  // Memoize the points data to prevent unnecessary recalculations
  const pointsData = useMemo(() => {
    return (
      data?.pages?.flatMap((page) =>
        page.data.map((v) => ({
          type: 'Feature' as const,
          geometry: {
            type: 'Point' as const,
            coordinates: [v.location.longitude, v.location.latitude],
          },
          properties: { ...v },
        })),
      ) || []
    );
  }, [data?.pages]);

  // Use clusterer with memoized points
  const [points, superClusterer] = useClusterer(
    pointsData,
    { height: SCREEN_HEIGHT, width: SCREEN_WIDTH },
    region,
  );

  // Handle region change with debounce
  const handleRegionChange = useCallback((newRegion: Region) => {
    setRegion(newRegion);
  }, []);

  // Ensure location updates after fetching user location
  useEffect(() => {
    if (userLocation) {
      setLocation({
        latitude: userLocation.latitude || 0,
        longitude: userLocation.longitude || 0,
        address: userLocation.fullAddress || '',
      });
    }
  }, [userLocation]);

  // Animate to location when it changes (with debounce)
  useEffect(() => {
    if (location && location.latitude && location.longitude) {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout for animation
      timeoutRef.current = setTimeout(() => {
        mapRef.current?.animateCamera(
          {
            center: {
              latitude: location.latitude,
              longitude: location.longitude,
            },
            zoom: 10, // Increased zoom for better focus
          },
          { duration: 500 },
        );
      }, 300); // Reduced delay for better responsiveness
    }

    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [location.latitude, location.longitude]); // Only run when coordinates change

  return (
    <View className='flex-1'>
      {/* Header and GoBack  */}
      <View
        className='absolute w-full z-10 px-2 gap-2'
        style={{ marginTop: inset.top }}
      >
        <GoBack iconColor={COLORS.neutral[800]} />

        <View className=' flex-1 w-full   py-3 px-2.5 rounded-xl  flex-row items-center'>
          <BlurView style={{ flex: 1 }} intensity={20} tint='light'>
            <GooglePlacesInput
              predefinedPlaces={[]}
              fetchDetails={true}
              renderLeftButton={() => (
                <View className='justify-center mx-2'>
                  <APP_Icons.SearchIcon width={24} height={24} />
                </View>
              )}
              textInputProps={{
                placeholderTextColor: COLORS.neutral[900],
                backgroundColor: 'transparent',
                fontWeight: '600',
              }}
              isRowScrollable={true}
              renderRow={(rowData) => {
                const title = rowData.structured_formatting.main_text;
                const address = rowData.structured_formatting.secondary_text;
                return <InputRow address={address} title={title} />;
              }}
              styles={{
                row: styles.row,
                container: styles.container,
                listView: styles.listView,
              }}
              debounce={500}
              keyboardShouldPersistTaps='always'
              enablePoweredByContainer={false}
              onPress={(data, details) => {
                if (!details) return;

                setLocation({
                  address: data.description,
                  latitude: details.geometry.location.lat,
                  longitude: details.geometry.location.lng,
                });
              }}
            />
          </BlurView>
        </View>
      </View>

      <MapView
        provider={PROVIDER_GOOGLE}
        style={StyleSheet.absoluteFillObject}
        ref={mapRef}
        onRegionChangeComplete={handleRegionChange}
        initialRegion={region}
        cameraZoomRange={{
          animated: true,
          maxCenterCoordinateDistance: 20,
          minCenterCoordinateDistance: 10,
        }}
        rotateEnabled={false}
        loadingEnabled={true}
        loadingIndicatorColor={COLORS.primary[200]}
        loadingBackgroundColor='rgba(255, 255, 255, 0.7)'
      >
        {/* User current location - memoized for performance */}
        {userLocation && (
          <Marker
            coordinate={{
              latitude: userLocation.latitude,
              longitude: userLocation.longitude,
            }}
            tracksViewChanges={false} // Performance optimization
          >
            <View style={styles.userMarker}>
              <View style={styles.userArrow} />
              <View style={styles.userInnerCircle} />
            </View>
          </Marker>
        )}

        {/* Render markers with optimized rendering */}
        {useMemo(
          () =>
            points.map((point) => {
              const isCluster = isPointCluster(point);

              if (isCluster) {
                // Memoized cluster marker
                return (
                  <Marker
                    key={`cluster-${point.id}`}
                    onPress={() => {
                      const region = point.properties.getExpansionRegion();
                      mapRef.current?.animateToRegion(region, 300);
                    }}
                    coordinate={{
                      latitude: point.geometry.coordinates[1],
                      longitude: point.geometry.coordinates[0],
                    }}
                    tracksViewChanges={false} // Performance optimization
                  >
                    <Pressable className='w-10 h-10 bg-primary-200 rounded-full justify-center items-center'>
                      <CustomText className='text-white-50 font-bold'>
                        {point.properties.point_count}
                      </CustomText>
                    </Pressable>
                  </Marker>
                );
              } else {
                // Use memoized marker component for individual points
                return (
                  <MemoizedMarker
                    key={`point-${point.properties._id}`}
                    id={point.properties._id}
                    image={point.properties.images[0]}
                    name={point.properties.name}
                    latitude={point.geometry.coordinates[1]}
                    longitude={point.geometry.coordinates[0]}
                  />
                );
              }
            }),
          [points, mapRef],
        )}
      </MapView>
    </View>
  );
};

export default HomeScreenMap;

// Optimized marker component with useCallback for event handlers
const MemoizedMarker = memo(
  ({
    latitude,
    longitude,
    name,
    image,
    id,
  }: {
    latitude: number;
    longitude: number;
    name: string;
    image: string;
    id: string;
  }) => {
    // Memoize the onPress handler to prevent recreating on each render
    const handlePress = useCallback(() => {
      router.replace(`/(protected)/activity/${id}`);
    }, [id]);

    return (
      <Marker
        onPress={handlePress}
        coordinate={{ latitude, longitude }}
        tracksViewChanges={false} // Performance optimization
      >
        <View style={styles.markerContainer}>
          <View style={styles.circle}>
            <Image
              source={{ uri: image }}
              style={styles.image}
              cachePolicy='memory-disk'
              transition={300}
            />
          </View>
          <View style={styles.arrow} />
        </View>
        <Callout tooltip style={styles.callout}>
          <CustomText numberOfLines={2} ellipsizeMode='tail'>
            {name}
          </CustomText>
        </Callout>
      </Marker>
    );
  },
  // Custom equality function to prevent unnecessary re-renders
  (prevProps, nextProps) => {
    return (
      prevProps.id === nextProps.id &&
      prevProps.latitude === nextProps.latitude &&
      prevProps.longitude === nextProps.longitude &&
      prevProps.image === nextProps.image
    );
  },
);

// Optimized styles with better organization and performance considerations
const styles = StyleSheet.create({
  // Map marker styles
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
    backgroundColor: '#DDD', // Placeholder color while loading
  },
  circle: {
    width: _circleSize,
    height: _circleSize,
    borderRadius: _circleSize / 2,
    backgroundColor: COLORS.primary[200],
    overflow: 'hidden',
    borderWidth: 5,
    borderColor: COLORS.primary[200],
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  arrow: {
    width: 0,
    height: 0,
    borderLeftWidth: _circleArrowStroke,
    borderRightWidth: _circleArrowStroke,
    borderTopWidth: _circleArrowStroke * 2,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: COLORS.primary[200],
    marginTop: -_circleArrowStroke + 6,
  },

  // User location marker styles
  userMarker: {
    backgroundColor: 'rgba(201, 98, 119, 0.3)', // More readable rgba format
    borderRadius: 50,
    width: _userCircle,
    height: _userCircle,
    justifyContent: 'center',
    alignItems: 'center',
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  userArrow: {
    width: 0,
    height: 0,
    borderWidth: _userArrowStroke,
    borderColor: COLORS.secondary[300],
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: 'transparent',
    position: 'absolute',
    top: -3,
  },
  userInnerCircle: {
    width: _userCircle / 2,
    height: _userCircle / 2,
    borderRadius: 14,
    backgroundColor: COLORS.secondary[300],
    borderWidth: 2,
    borderColor: 'white',
  },

  // Callout styles
  callout: {
    width: 180, // Increased width for better readability
    padding: 10,
    borderRadius: 10,
    backgroundColor: COLORS.white[50],
    justifyContent: 'center',
    alignItems: 'center',
    // Add shadow for better visibility
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
  },

  // Google search input styles
  listView: {
    backgroundColor: 'rgba(172, 172, 172, 0.6)',
    borderRadius: 20,
    marginHorizontal: 5,
    marginTop: 5,
    maxHeight: SCREEN_HEIGHT * 0.4, // Limit height to prevent covering the whole screen
  },
  row: {
    backgroundColor: 'rgba(172, 172, 172, 0.6)',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
  },
  container: {
    backgroundColor: 'rgba(172, 172, 172, 0.6)',
    borderWidth: 1,
    borderColor: COLORS.primary[50],
    borderRadius: 20,
    overflow: 'hidden',
  },
});
