import { useEffect } from 'react';
import { useSession } from '@/context/AuthContext';
import useGetBlockedUsers from './useGetBlockedUsers';

/**
 * Hook to initialize blocked users data when the user is authenticated
 * This should be called at the app level to ensure blocked users are loaded
 * when the user logs in or when the app starts with an authenticated user
 */
const useInitializeBlockedUsers = () => {
  const { userId, user } = useSession();

  // Fetch blocked users when user is authenticated
  const { isLoading, error, blockedUsers } = useGetBlockedUsers({
    enabled: !!userId && !!user,
    staleTime: 10 * 60 * 1000, // 10 minutes - blocked users don't change frequently
    gcTime: 30 * 60 * 1000, // 30 minutes cache time
  });

  // Log initialization status for debugging
  useEffect(() => {
    if (userId && user) {
      console.log('Initializing blocked users for user:', userId);
    }
  }, [userId, user]);

  useEffect(() => {
    if (blockedUsers.length > 0) {
      console.log(`Loaded ${blockedUsers.length} blocked users`);
    }
  }, [blockedUsers.length]);

  return {
    isInitializing: isLoading,
    initializationError: error,
    blockedUsersCount: blockedUsers.length,
  };
};

export default useInitializeBlockedUsers;
